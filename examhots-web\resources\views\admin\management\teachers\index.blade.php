@extends('main')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/management-style.css') }}">
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
            </svg>
            <h1 class="page-title text-3xl font-bold text-gray-800">Manaj<PERSON>en Guru</h1>
        </div>

        <div class="page-header-actions flex items-center space-x-4">
            <!-- Search Bar -->
            <div class="search-container relative">
                <input type="text" id="searchInput" placeholder="Cari guru di sini..."
                    class="search-input w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <!-- Add Button -->
            <button onclick="openCreateModal()"
                class="btn-primary bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Tambah Guru Baru</span>
            </button>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="cardsContainer">
        @forelse ($teachers as $teacher)
            <div class="card-hover bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow teacher-card"
                data-name="{{ strtolower($teacher->name) }}" data-email="{{ strtolower($teacher->email) }}"
                data-nip="{{ strtolower($teacher->nip) }}">

                <!-- Card Header -->
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-primary-blue rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">{{ $teacher->name }}</h3>
                            <p class="text-sm text-gray-500">NIP: {{ $teacher->nip }}</p>
                        </div>
                    </div>

                    <!-- Dropdown Menu -->
                    <div class="relative">
                        <button
                            class="dropdown-toggle text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
                            data-dropdown="dropdown-{{ $teacher->id }}">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                                </path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="dropdown-{{ $teacher->id }}"
                            class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10 hidden">
                            <div class="py-1">
                                <a href="{{ route('teachers.edit', $teacher->id) }}"
                                    onclick="openEditModal(event, this.href); return false;"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                    <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit Guru
                                </a>
                                <a href="https://wa.me/{{ $teacher->phonenumber }}" target="_blank"
                                    class="flex items-center px-4 py-2 text-sm text-green-600 hover:bg-green-50 transition-colors">
                                    <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.787">
                                        </path>
                                    </svg>
                                    WhatsApp
                                </a>
                                <button onclick="deleteTeacher({{ $teacher->id }}, '{{ $teacher->name }}')"
                                    class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                    Hapus Guru
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="space-y-3">
                    <!-- Email -->
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                            </path>
                        </svg>
                        <span>{{ $teacher->email }}</span>
                    </div>

                    <!-- Gender -->
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>{{ $teacher->gender === 'female' ? 'Perempuan' : 'Laki-laki' }}</span>
                    </div>

                    <!-- Phone -->
                    @if ($teacher->phonenumber)
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                </path>
                            </svg>
                            <span>{{ $teacher->phonenumber }}</span>
                        </div>
                    @endif

                    <!-- Address -->
                    @if ($teacher->address)
                        <div class="flex items-start text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span class="line-clamp-2">{{ $teacher->address }}</span>
                        </div>
                    @endif

                    <!-- Created Date -->
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        <span>Bergabung {{ $teacher->created_at ? $teacher->created_at->format('d M Y') : '-' }}</span>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-500">ID: {{ $teacher->id }}</span>
                        <div class="flex space-x-2">
                            <a href="{{ route('teachers.edit', $teacher->id) }}"
                                onclick="openEditModal(event, this.href); return false;"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <!-- Empty State -->
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada guru</h3>
                <p class="text-gray-500 mb-6">Mulai dengan menambahkan guru pertama Anda</p>
                <button onclick="openCreateModal()"
                    class="bg-primary-blue text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 transition-all">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Tambah Guru Baru</span>
                </button>
            </div>
        @endforelse
    </div>

    <!-- Hidden form for delete -->
    <form id="deleteForm" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>
@endsection

@section('script')
    <script src="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.js') }}"></script>
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.teacher-card');

            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                const email = card.getAttribute('data-email');
                const nip = card.getAttribute('data-nip');

                if (name.includes(searchTerm) || email.includes(searchTerm) || nip.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Dropdown functionality
        document.addEventListener('click', function(e) {
            // Close all dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });

            // Open clicked dropdown
            if (e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                const button = e.target.closest('.dropdown-toggle');
                const dropdownId = button.getAttribute('data-dropdown');
                const dropdown = document.getElementById(dropdownId);
                dropdown.classList.toggle('hidden');
            }
        });

        // Modal functions
        function openCreateModal() {
            console.log('Opening create modal');

            fetch("{{ route('teachers.add') }}")
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form tambah guru. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function openEditModal(event, url) {
            event.preventDefault();
            event.stopPropagation();

            console.log('Opening edit modal for URL:', url);

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form edit. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function setupModalListeners() {
            // Focus on first input
            const firstInput = document.querySelector(
                '#modalContent input[type="text"], #modalContent input[type="email"]');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Form validation
            const form = document.querySelector('#modalContent form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const nameField = form.querySelector('input[name="name"]');
                    const nipField = form.querySelector('input[name="nip"]');

                    if (nameField && nameField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Nama harus diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        nameField.focus();
                        return;
                    }

                    if (nipField && nipField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'NIP harus diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        nipField.focus();
                        return;
                    }
                });
            }
        }

        function closeModal() {
            const modal = document.getElementById('modalGlobal');
            const content = document.getElementById('modalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = '';
            }, 200);
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('modalGlobal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('modalGlobal');
                if (!modal.classList.contains('hidden')) {
                    closeModal();
                }
            }
        });

        // Delete function
        function deleteTeacher(id, name) {
            Swal.fire({
                title: 'Apakah Anda yakin?',
                text: `Guru "${name}" akan dihapus permanen!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.getElementById('deleteForm');
                    form.action = `/management/teachers/delete/${id}`;
                    form.submit();
                }
            });
        }

        // Show success/error messages
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: '{{ session('error') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if ($errors->any())
            let errorMessages = '';
            @foreach ($errors->all() as $error)
                errorMessages += '• {{ $error }}\n';
            @endforeach

            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                html: '<div style="text-align: left; white-space: pre-line;">' + errorMessages + '</div>',
                timer: 8000,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        @endif
    </script>
@endsection
