{{-- @extends('main')

@section('content')
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8"><PERSON><PERSON><PERSON></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html"><PERSON><PERSON>an</a></li>
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a></li>
                            <li class="breadcrumb-item" aria-current="page">Edit Bank Soal</li>
                        </ol>
                    </nav>
                </div>

                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('question.edit.post', $question->id) }}" method="POST" autocomplete="off">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Bank Soal</label><span
                                        class="text-danger">*</span>
                                    <input type="text" class="form-control" id="name" name="name"
                                        value="{{ $question->name }}" placeholder="Masukkan Nama" required>
                                    @error('name')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Deskripsi</label><span
                                        class="text-danger">*</span>
                                    <textarea name="description" id="description" class="form-control" placeholder="Masukkan Deskripsi" required>{{ $question->description }}</textarea>
                                    @error('description')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <a href="{{ route('question.index') }}" class="btn btn-danger"><i class="ti ti-arrow-left"></i>
                                Kembali</a>
                            <button class="btn btn-primary">Submit <i class="ti ti-send"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection --}}

<div class="flex items-center justify-between p-6 border-b border-gray-100">
    <div class="flex items-center space-x-4">
        <!-- Icon -->
        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
            </svg>
        </div>
        <!-- Title and Subtitle -->
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Ubah Bank Soal</h2>
            <p class="text-sm text-gray-500 mt-1">Beri judul dan deskripsi singkat untuk memulainya</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors" onclick="closeModal()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>
<form action="{{ route('question.edit.post', $question->id) }}" method="POST" autocomplete="off">
    @csrf
    @method('PUT')
    <!-- Modal Body -->
    <div class="p-6 space-y-6">
        <!-- Judul Bank Soal -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Judul Bank Soal<span class="text-red-500 ml-1">*</span>
            </label>
            <input type="text" id="name" name="name" placeholder="e.g.UTS IPA Kelas 8 SMP Semester Genap"
                value="{{ $question->name }}"
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200">
        </div>

        <!-- Deskripsi Bank Soal -->
        <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Deskripsi Bank Soal
            </label>
            <textarea id="description" name="description" rows="4" maxlength="200"
                placeholder="e.g. Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran darah manusia."
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200 resize-none">{{ $question->description }}</textarea>
            <div class="flex justify-between items-center mt-2">
                <span class="text-sm text-gray-500">Masukkan deskripsi</span>
                <span class="text-sm text-gray-400" id="charCount">0/200</span>
            </div>
        </div>

        <!-- Teacher Selection (Admin Only) -->
        @if ($isAdmin)
            <div>
                <label for="teacher_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Pilih Guru<span class="text-red-500 ml-1">*</span>
                </label>
                <select id="teacher_id" name="teacher_id"
                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200">
                    <option value="">-- Pilih Guru --</option>
                    @foreach ($teachers as $teacher)
                        <option value="{{ $teacher->id }}"
                            {{ $question->teacher_id == $teacher->id ? 'selected' : '' }}>
                            {{ $teacher->name }} ({{ $teacher->email }})
                        </option>
                    @endforeach
                </select>
            </div>
        @endif
    </div>

    <!-- Modal Footer -->
    <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-100">
        <button type="button" onclick="closeModal()"
            class="px-6 py-2.5 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="saveBtn" type="submit"
            class="px-6 py-2.5 bg-primary-blue text-white rounded-xl hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
            Simpan & Lanjutkan
        </button>
    </div>
</form>
