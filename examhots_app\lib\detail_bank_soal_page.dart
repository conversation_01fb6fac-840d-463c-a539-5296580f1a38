import 'package:examhots_app/config/app_config.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'buat_soal_baru_page.dart';
import 'edit_question_page.dart';
import 'models/question_material.dart';
import 'models/question.dart';
import 'providers/question_material_provider.dart';
import 'providers/question_provider.dart';

class DetailBankSoalPage extends StatefulWidget {
  final QuestionMaterial? material;
  final String title;
  final String description;
  final int? pgCount;
  final int? essayCount;
  final int? singkatCount;

  const DetailBankSoalPage({
    super.key,
    this.material,
    required this.title,
    required this.description,
    this.pgCount,
    this.essayCount,
    this.singkatCount,
  });

  @override
  State<DetailBankSoalPage> createState() => _DetailBankSoalPageState();
}

class _DetailBankSoalPageState extends State<DetailBankSoalPage>
    with SingleTickerProviderStateMixin {
  // Temporary state for editing
  String? _tempTitle;
  String? _tempDescription;
  bool _isEditMode = false;
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;

  // Tab controller
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });

    // Load questions if material is available
    if (widget.material != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<QuestionProvider>().loadQuestionsByMaterialId(
          widget.material!.id,
        );
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showEditDialog() {
    setState(() {
      _tempTitle = widget.title;
      _tempDescription = widget.description;
      _isEditMode = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildEditDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Detail Bank Soal',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Header Card
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              image: const DecorationImage(
                image: AssetImage('assets/image-bg.jpg'),
                fit: BoxFit.cover,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Score Badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF455A9D),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'TOTAL SKOR : -',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Title and Edit Button
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        _tempTitle ?? widget.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                          height: 1.3,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (widget.material != null)
                      GestureDetector(
                        onTap: _showEditDialog,
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Color(0xFF455A9D),
                            size: 20,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),

                // Description
                Text(
                  _tempDescription ?? widget.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF666666),
                    height: 1.4,
                  ),
                ),

                // Unsaved changes indicator
                if (_hasUnsavedChanges)
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.edit, size: 16, color: Colors.orange[700]),
                        const SizedBox(width: 6),
                        Text(
                          'Ada perubahan yang belum disimpan',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 20),

                // Search Bar and Add Button
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const TextField(
                          decoration: InputDecoration(
                            prefixIcon: Icon(
                              Icons.search,
                              color: Color(0xFF999999),
                              size: 20,
                            ),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            hintText: 'Cari soal di sini...',
                            hintStyle: TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 14,
                            ),
                          ),
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    GestureDetector(
                      onTap: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => BuatSoalBaruPage(
                                  material: widget.material!,
                                ),
                          ),
                        );

                        // Refresh questions if a new question was added
                        if (result == true && mounted) {
                          if (context.mounted) {
                            context.read<QuestionProvider>().refresh();
                          }
                        }
                      },
                      child: Container(
                        width: 44,
                        height: 44,
                        decoration: const BoxDecoration(
                          color: Color(0xFF455A9D),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Tab Bar and Content
          Expanded(
            child: Consumer<QuestionProvider>(
              builder: (context, questionProvider, child) {
                if (questionProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: Color(0xFF455A9D)),
                  );
                }

                if (questionProvider.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error: ${questionProvider.errorMessage}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => questionProvider.refresh(),
                          child: const Text('Coba Lagi'),
                        ),
                      ],
                    ),
                  );
                }

                if (questionProvider.hasNoQuestions) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Spacer(),
                      // Empty State Content
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: Column(
                          children: [
                            // No data GIF
                            Image.asset(
                              'assets/no-data.gif',
                              width: 120,
                              height: 120,
                              fit: BoxFit.contain,
                            ),
                            const SizedBox(height: 24),
                            const Text(
                              'Bank Soal Ini Masih Kosong',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF333333),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            RichText(
                              textAlign: TextAlign.center,
                              text: const TextSpan(
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF666666),
                                  height: 1.4,
                                ),
                                children: [
                                  TextSpan(
                                    text: 'Belum ada soal yang ditambahkan. ',
                                  ),
                                  TextSpan(
                                    text: 'Ayo buat soal pertamamu sekarang!',
                                    style: TextStyle(
                                      color: Color(0xFF455A9D),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                    ],
                  );
                }

                // Show tabs and content when there are questions
                return Column(
                  children: [
                    // Tab Bar
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: TabBar(
                        controller: _tabController,
                        labelColor: const Color(0xFF455A9D),
                        unselectedLabelColor: const Color(0xFF999999),
                        indicatorColor: const Color(0xFF455A9D),
                        indicatorWeight: 2,
                        labelStyle: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        unselectedLabelStyle: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        tabs: [
                          Tab(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text('Pilihan Ganda'),
                                const SizedBox(width: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF455A9D),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${questionProvider.pgCount}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Tab(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text('Uraian Singkat'),
                                const SizedBox(width: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF455A9D),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${questionProvider.uraianCount}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Tab(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text('Esai'),
                                const SizedBox(width: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF455A9D),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${questionProvider.esaiCount}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Tab Content
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildPilihanGandaTab(
                            questionProvider.pilihanGandaQuestions,
                          ),
                          _buildUraianSingkatTab(
                            questionProvider.uraianSingkatQuestions,
                          ),
                          _buildEsaiTab(questionProvider.esaiQuestions),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: Consumer<QuestionProvider>(
        builder: (context, questionProvider, child) {
          // Only show bottom navigation when there are questions
          if (questionProvider.hasNoQuestions) {
            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: const BorderSide(color: Color(0xFF455A9D)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Batal',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF455A9D),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveToAPI,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF455A9D),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                : Text(
                                  _hasUnsavedChanges
                                      ? 'Simpan Perubahan'
                                      : 'Simpan',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // Show score navigation when there are questions
          return _buildScoreBottomNavigation(questionProvider);
        },
      ),
    );
  }

  // Handle question menu actions
  void _handleQuestionMenuAction(String action, Question question) {
    switch (action) {
      case 'edit':
        _editQuestion(question);
        break;
      case 'delete':
        _showDeleteQuestionDialog(question);
        break;
    }
  }

  // Edit question
  Future<void> _editQuestion(Question question) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditQuestionPage(question: question),
      ),
    );

    // Refresh questions if question was updated
    if (result == true && mounted) {
      if (context.mounted) {
        context.read<QuestionProvider>().refresh();
      }
    }
  }

  // Show delete question confirmation dialog
  void _showDeleteQuestionDialog(Question question) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text(
              'Hapus Pertanyaan',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            content: const Text(
              'Apakah Anda yakin ingin menghapus pertanyaan ini? Tindakan ini tidak dapat dibatalkan.',
              style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Batal',
                  style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteQuestion(question);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Hapus',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  // Delete question
  Future<void> _deleteQuestion(Question question) async {
    try {
      final provider = context.read<QuestionProvider>();
      final success = await provider.deleteQuestion(question.id);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Pertanyaan berhasil dihapus!'),
              backgroundColor: Color(0xFF455A9D),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                provider.errorMessage ?? 'Gagal menghapus pertanyaan',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Widget _buildEditDialog() {
    final titleController = TextEditingController(text: _tempTitle);
    final descriptionController = TextEditingController(text: _tempDescription);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Edit Bank Soal',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isEditMode = false;
                            _tempTitle = null;
                            _tempDescription = null;
                          });
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: const BoxDecoration(
                            color: Color(0xFFF5F5F5),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Color(0xFF666666),
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Title Field
                  const Text(
                    'Judul Bank Soal*',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFE0E0E0)),
                    ),
                    child: TextField(
                      controller: titleController,
                      onChanged: (value) {
                        setState(() {
                          _tempTitle = value;
                        });
                      },
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                        hintText: 'Masukkan judul bank soal',
                        hintStyle: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 16,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Description Field
                  const Text(
                    'Deskripsi Bank Soal',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFE0E0E0)),
                    ),
                    child: TextField(
                      controller: descriptionController,
                      maxLines: 4,
                      onChanged: (value) {
                        setState(() {
                          _tempDescription = value;
                        });
                      },
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                        hintText: 'Masukkan deskripsi bank soal',
                        hintStyle: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 16,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom Navigation Bar
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
                border: Border(
                  top: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _isEditMode = false;
                          _tempTitle = null;
                          _tempDescription = null;
                        });
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Color(0xFF455A9D)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Batal',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF455A9D),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _saveTemporaryChanges,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF455A9D),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text(
                                'Simpan',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveTemporaryChanges() {
    if (_tempTitle == null || _tempTitle!.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Judul bank soal harus diisi!'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_tempDescription == null || _tempDescription!.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Deskripsi bank soal harus diisi!'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isEditMode = false;
      _hasUnsavedChanges = true;
    });
    Navigator.of(context).pop(); // Close dialog

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Perubahan disimpan sementara. Klik "Simpan" untuk menyimpan ke database.',
        ),
        backgroundColor: Color(0xFF455A9D),
        duration: Duration(seconds: 3),
      ),
    );
  }

  Future<void> _saveToAPI() async {
    if (!_hasUnsavedChanges) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tidak ada perubahan untuk disimpan.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (widget.material == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Data bank soal tidak ditemukan!'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final provider = context.read<QuestionMaterialProvider>();
      final success = await provider.updateQuestionMaterial(
        widget.material!.id,
        _tempTitle?.trim() ?? widget.title,
        _tempDescription?.trim() ?? widget.description,
      );

      if (success) {
        if (mounted) {
          setState(() {
            _hasUnsavedChanges = false;
            _tempTitle = null;
            _tempDescription = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Bank soal berhasil diperbarui!'),
              backgroundColor: Color(0xFF455A9D),
            ),
          );
          // Refresh the page by popping and pushing again with updated data
          Navigator.of(context).pop(); // Go back to home
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                provider.errorMessage ?? 'Gagal memperbarui bank soal',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Build tab content for Pilihan Ganda
  Widget _buildPilihanGandaTab(List<Question> questions) {
    if (questions.isEmpty) {
      return const Center(
        child: Text(
          'Belum ada soal pilihan ganda',
          style: TextStyle(fontSize: 16, color: Color(0xFF666666)),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: questions.length,
      itemBuilder: (context, index) {
        final question = questions[index];
        return _buildQuestionCard(question, index);
      },
    );
  }

  // Build tab content for Uraian Singkat
  Widget _buildUraianSingkatTab(List<Question> questions) {
    if (questions.isEmpty) {
      return const Center(
        child: Text(
          'Belum ada soal uraian singkat',
          style: TextStyle(fontSize: 16, color: Color(0xFF666666)),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: questions.length,
      itemBuilder: (context, index) {
        final question = questions[index];
        return _buildQuestionCard(question, index);
      },
    );
  }

  // Build tab content for Esai
  Widget _buildEsaiTab(List<Question> questions) {
    if (questions.isEmpty) {
      return const Center(
        child: Text(
          'Belum ada soal esai',
          style: TextStyle(fontSize: 16, color: Color(0xFF666666)),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: questions.length,
      itemBuilder: (context, index) {
        final question = questions[index];
        return _buildQuestionCard(question, index);
      },
    );
  }

  // Build question card
  Widget _buildQuestionCard(Question question, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question type badge and menu
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getQuestionTypeColor(question.type),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getQuestionTypeLabel(question.type),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              if (question.type == 'esai')
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF455A9D),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Skor : ${question.answers.isNotEmpty && question.answers[0].score != null ? question.answers[0].score : 0}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              const Spacer(),
              PopupMenuButton<String>(
                onSelected:
                    (value) => _handleQuestionMenuAction(value, question),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(
                              Icons.edit,
                              color: Color(0xFF455A9D),
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red, size: 20),
                            SizedBox(width: 8),
                            Text('Hapus'),
                          ],
                        ),
                      ),
                    ],
                icon: const Icon(Icons.more_vert, color: Color(0xFF666666)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                color: Colors.white,
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Question text
          Text(
            question.question,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
              height: 1.4,
            ),
          ),

          // Show images if available
          if (question.img != null && question.img!.isNotEmpty) ...[
            const SizedBox(height: 12),
            // Display actual images
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  question.img!
                      .split(',')
                      .where((img) => img.trim().isNotEmpty)
                      .map((img) {
                        final imageUrl = AppConfig.getQuestionImageUrl(
                          img.trim(),
                        );

                        return Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: const Color(0xFFE0E0E0)),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: const Color(0xFFF5F5F5),
                                  child: const Center(
                                    child: Icon(
                                      Icons.broken_image,
                                      size: 24,
                                      color: Color(0xFF999999),
                                    ),
                                  ),
                                );
                              },
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  color: const Color(0xFFF5F5F5),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Color(0xFF455A9D),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      })
                      .toList(),
            ),
          ],

          // Show answers for pilihan ganda
          if (question.isPilihanGanda && question.answers.isNotEmpty) ...[
            const SizedBox(height: 12),
            ...question.answers.asMap().entries.map((entry) {
              final index = entry.key;
              final answer = entry.value;
              final optionLabel = String.fromCharCode(
                65 + index,
              ); // A, B, C, D, E

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      answer.isCorrect
                          ? const Color(0xFFE8F5E8)
                          : const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        answer.isCorrect
                            ? const Color(0xFF4CAF50)
                            : const Color(0xFFE0E0E0),
                  ),
                ),
                child: Row(
                  children: [
                    // Option label (A, B, C, D, E)
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color:
                            answer.isCorrect
                                ? const Color(0xFF4CAF50)
                                : const Color(0xFFE0E0E0),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          optionLabel,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color:
                                answer.isCorrect
                                    ? Colors.white
                                    : const Color(0xFF666666),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Checkmark icon for correct answer
                    if (answer.isCorrect) ...[
                      const Icon(
                        Icons.check_circle,
                        color: Color(0xFF4CAF50),
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                    ],
                    // Answer text
                    Expanded(
                      child: Text(
                        answer.answer,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight:
                              answer.isCorrect
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                          color:
                              answer.isCorrect
                                  ? const Color(0xFF2E7D32)
                                  : const Color(0xFF666666),
                        ),
                      ),
                    ),
                    // "Kunci Jawaban" label for correct answer
                    if (answer.isCorrect) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4CAF50),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Kunci Jawaban',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              );
            }),
          ],

          // Show answer for uraian singkat and esai
          if ((question.isUraianSingkat || question.isEsai) &&
              question.answers.isNotEmpty) ...[
            const SizedBox(height: 12),
            // Answer label
            const Row(
              children: [
                Text(
                  'Jawaban:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Answer container
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFE8F5E8),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFF4CAF50)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Checkmark icon
                  const Icon(
                    Icons.check_circle,
                    color: Color(0xFF4CAF50),
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  // Answer text
                  Expanded(
                    child: Text(
                      question.answers.first.answer,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Get question type color
  Color _getQuestionTypeColor(String type) {
    switch (type) {
      case 'pilihan_ganda':
        return const Color(0xFF455A9D);
      case 'uraian_singkat':
        return const Color(0xFFFFA726);
      case 'esai':
        return const Color(0xFF66BB6A);
      default:
        return const Color(0xFF999999);
    }
  }

  // Get question type label
  String _getQuestionTypeLabel(String type) {
    switch (type) {
      case 'pilihan_ganda':
        return 'Pilihan Ganda';
      case 'uraian_singkat':
        return 'Uraian Singkat';
      case 'esai':
        return 'Essay';
      default:
        return type;
    }
  }

  // Build score bottom navigation
  Widget _buildScoreBottomNavigation(QuestionProvider questionProvider) {
    String scoreText = '';
    String scoreLabel = '';
    double currentScore = 0.0;
    bool isEditable = true; // Control if score can be edited

    // Determine which score to show based on current tab
    switch (_currentTabIndex) {
      case 0: // Pilihan Ganda
        scoreText = 'Total Skor Pilihan Ganda*';
        scoreLabel = 'e.g.100 (MAX 100)';
        currentScore = questionProvider.pgTotalScore;
        isEditable = true;
        break;
      case 1: // Uraian Singkat
        scoreText = 'Total Skor Uraian Singkat*';
        scoreLabel = 'e.g.100 (MAX 100)';
        currentScore = questionProvider.uraianTotalScore;
        isEditable = true;
        break;
      case 2: // Esai
        scoreText = 'Total Skor Esai*';
        scoreLabel = 'e.g.100 (MAX 100)';
        currentScore = questionProvider.esaiTotalScore;
        isEditable = false; // Esai score cannot be edited
        break;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              scoreText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: isEditable ? Colors.white : const Color(0xFFF9FAFB),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE0E0E0)),
              ),
              child: TextField(
                enabled: isEditable,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  hintText: isEditable ? scoreLabel : 'Tidak dapat diubah',
                  hintStyle: TextStyle(
                    color:
                        isEditable
                            ? const Color(0xFF999999)
                            : const Color(0xFFBBBBBB),
                    fontSize: 16,
                  ),
                ),
                style: TextStyle(
                  fontSize: 16,
                  color:
                      isEditable
                          ? const Color(0xFF333333)
                          : const Color(0xFF999999),
                ),
                keyboardType: isEditable ? TextInputType.number : null,
                controller: TextEditingController(
                  text: currentScore > 0 ? currentScore.toString() : '',
                ),
                onChanged:
                    isEditable
                        ? (value) {
                          final score = double.tryParse(value) ?? 0.0;
                          switch (_currentTabIndex) {
                            case 0:
                              questionProvider.updatePgScore(score);
                              break;
                            case 1:
                              questionProvider.updateUraianScore(score);
                              break;
                            case 2:
                              // Esai score cannot be changed here
                              break;
                          }
                        }
                        : null,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: const BorderSide(color: Color(0xFF455A9D)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Batal',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF455A9D),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed:
                        isEditable ? () => _saveScores(questionProvider) : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isEditable
                              ? const Color(0xFF455A9D)
                              : const Color(0xFFCCCCCC),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child:
                        questionProvider.isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                            : Text(
                              'Simpan',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color:
                                    isEditable
                                        ? Colors.white
                                        : const Color(0xFF999999),
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveScores(QuestionProvider questionProvider) async {
    try {
      final success = await questionProvider.saveScoresToDatabase();

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Skor berhasil disimpan!'),
              backgroundColor: Color(0xFF455A9D),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                questionProvider.errorMessage ?? 'Gagal menyimpan skor',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
