class Question {
  final int id;
  final int questionMaterialId;
  final String question;
  final String type; // 'pilihan_ganda', 'uraian_singkat', 'esai'
  final String? img;
  final List<Answer> answers;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Question({
    required this.id,
    required this.questionMaterialId,
    required this.question,
    required this.type,
    this.img,
    this.answers = const [],
    this.createdAt,
    this.updatedAt,
  });

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: _parseToInt(json['id']),
      questionMaterialId: _parseToInt(json['questionmaterialid']),
      question: json['question'] ?? '',
      type: json['type'] ?? '',
      img: json['img'],
      answers:
          json['answers'] != null
              ? (json['answers'] as List)
                  .map((answerJson) => Answer.fromJson(answerJson))
                  .toList()
              : [],
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionmaterialid': questionMaterialId,
      'question': question,
      'type': type,
      'img': img,
      'answers': answers.map((answer) => answer.toJson()).toList(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper methods
  bool get isPilihanGanda => type == 'pilihan_ganda';
  bool get isUraianSingkat => type == 'uraian_singkat';
  bool get isEsai => type == 'esai';

  // Get correct answer for pilihan ganda
  Answer? get correctAnswer {
    if (!isPilihanGanda) return null;
    try {
      return answers.firstWhere((answer) => answer.isCorrect);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'Question{id: $id, questionMaterialId: $questionMaterialId, question: $question, type: $type, answersCount: ${answers.length}}';
  }
}

class Answer {
  final int id;
  final int questionId;
  final String answer;
  final bool isCorrect;
  final int? score;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Answer({
    required this.id,
    required this.questionId,
    required this.answer,
    this.isCorrect = false,
    this.score,
    this.createdAt,
    this.updatedAt,
  });

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  // Helper method to safely parse dynamic values to nullable int
  static int? _parseToNullableInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  // Helper method to safely parse dynamic values to bool
  static bool _parseToBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      return lowerValue == 'true' || lowerValue == '1';
    }
    return false;
  }

  factory Answer.fromJson(Map<String, dynamic> json) {
    return Answer(
      id: _parseToInt(json['id']),
      questionId: _parseToInt(json['questionid']),
      answer: json['answer'] ?? '',
      isCorrect: _parseToBool(json['is_correct']),
      score: _parseToNullableInt(json['score']),
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionid': questionId,
      'answer': answer,
      'is_correct': isCorrect ? 1 : 0,
      'score': score,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'Answer{id: $id, questionId: $questionId, answer: $answer, isCorrect: $isCorrect}';
  }
}
