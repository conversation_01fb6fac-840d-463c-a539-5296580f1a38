<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UsersController;
use App\Http\Controllers\TeachersController;
use App\Http\Controllers\ClassController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\ScheduleExamController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\ExportController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    if (Auth::check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('auth.login');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Auth
Route::get('auth/login', [AuthController::class, 'index'])->name('auth.login');
Route::post('auth/login', [AuthController::class, 'processLogin'])->name('auth.login.post');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Admin-only management routes
Route::middleware(['web.admin'])->group(function () {
    // Crud Users
    Route::get('management/users', [UsersController::class, 'index'])->name('users.index');
    Route::get('management/users/add', [UsersController::class, 'add'])->name('users.add');
    Route::post('management/users/add', [UsersController::class, 'processAdd'])->name('users.add.post');
    Route::get('management/edit/{id}', [UsersController::class, 'edit'])->name('users.edit');
    Route::put('management/edit/{id}', [UsersController::class, 'processEdit'])->name('users.edit.post');
    Route::delete('management/delete/{id}', [UsersController::class, 'delete'])->name('users.delete');

    // Crud Teachers
    Route::get('management/teachers', [TeachersController::class, 'index'])->name('teachers.index');
    Route::get('management/teachers/add', [TeachersController::class, 'add'])->name('teachers.add');
    Route::post('management/teachers/add', [TeachersController::class, 'processAdd'])->name('teachers.add.post');
    Route::get('management/teachers/edit/{id}', [TeachersController::class, 'edit'])->name('teachers.edit');
    Route::put('management/teachers/edit/{id}', [TeachersController::class, 'processEdit'])->name('teachers.edit.post');
    Route::delete('management/teachers/delete/{id}', [TeachersController::class, 'delete'])->name('teachers.delete');

    // Crud Class
    Route::get('management/class', [ClassController::class, 'index'])->name('class.index');
    Route::get('management/class/add', [ClassController::class, 'add'])->name('class.add');
    Route::post('management/class/add', [ClassController::class, 'processAdd'])->name('class.add.post');
    Route::get('management/class/edit/{id}', [ClassController::class, 'edit'])->name('class.edit');
    Route::put('management/class/edit/{id}', [ClassController::class, 'processEdit'])->name('class.edit.post');
    Route::delete('management/class/delete/{id}', [ClassController::class, 'delete'])->name('class.delete');

    //Crud Student
    Route::get('management/student', [StudentController::class, 'index'])->name('student.index');
    Route::get('management/student/add', [StudentController::class, 'add'])->name('student.add');
    Route::post('management/student/add', [StudentController::class, 'processAdd'])->name('student.add.post');
    Route::get('management/student/edit/{id}', [StudentController::class, 'edit'])->name('student.edit');
    Route::put('management/student/edit/{id}', [StudentController::class, 'processEdit'])->name('student.edit.post');
    Route::delete('management/student/delete/{id}', [StudentController::class, 'delete'])->name('student.delete');
});

//Bank Soal
Route::get('questionbank/question', [QuestionController::class, 'index'])->name('question.index');
Route::get('questionbank/question/add', [QuestionController::class, 'add'])->name('question.add');
Route::post('questionbank/question/add', [QuestionController::class, 'processAdd'])->name('question.add.post');
Route::get('questionbank/question/edit/{id}', [QuestionController::class, 'edit'])->name('question.edit');
Route::put('questionbank/question/edit/{id}', [QuestionController::class, 'processEdit'])->name('question.edit.post');
Route::delete('questionbank/question/delete/{id}', [QuestionController::class, 'delete'])->name('question.delete');
Route::get('questionbank/question/detail/{materialid}', [QuestionController::class, 'detail'])->name('question.detail');
Route::get('questionbank/question/detail/{materialid}/add', [QuestionController::class, 'addDetail'])->name('question.detail.add'); // show form
Route::post('questionbank/question/detail/{materialid}/choose/', [QuestionController::class, 'chooseDetailType'])->name('question.detail.choose');
Route::get('questionbank/question/detail/{materialid}/add/{type}', [QuestionController::class, 'addDetailType'])->name('question.detail.add.type');
Route::post('questionbank/question/detail/{materialid}/add/{type}', [QuestionController::class, 'processAddDetailType'])->name('question.detail.add.type.post');
Route::get('questionbank/question/detail/{questionid}/edit', [QuestionController::class, 'editDetailType'])->name('question.detail.edit.type');
Route::put('questionbank/question/detail/{questionid}/edit/{type}', [QuestionController::class, 'processEditDetailType'])->name('question.detail.edit.post');
Route::delete('questionbank/question/detail/{questionid}/delete', [QuestionController::class, 'deleteDetail'])->name('question.detail.delete');
Route::put('questionbank/material/{materialid}/scores', [QuestionController::class, 'updateMaterialScores'])->name('question.material.update.scores');
Route::post('/upload-image', [QuestionController::class, 'upload'])->name('upload.image')->middleware('web');

// Jadwal Ujian
Route::get('exam/schedule', [ScheduleExamController::class, 'index'])->name('exam.index');
Route::get('exam/schedule/add', [ScheduleExamController::class, 'add'])->name('exam.add');
Route::post('exam/schedule/add', [ScheduleExamController::class, 'processAdd'])->name('exam.add.post');
Route::get('exam/schedule/edit/{id}', [ScheduleExamController::class, 'edit'])->name('exam.edit');
Route::put('exam/schedule/edit/{id}', [ScheduleExamController::class, 'processEdit'])->name('exam.edit.post');
Route::delete('exam/schedule/delete/{id}', [ScheduleExamController::class, 'delete'])->name('exam.delete');
Route::get('/exam/question/table/{examId}', [ScheduleExamController::class, 'QuestionTable'])->name('exam.question.tabel');
Route::get('/exam/student/{id}', [ScheduleExamController::class, 'student'])->name('exam.student');
Route::get('/exam/{examId}/student/{studentId}/detail', [ScheduleExamController::class, 'studentDetail'])->name('exam.student.detail');
Route::post('/exam/{examId}/student/{studentId}/essay-score', [ScheduleExamController::class, 'updateEssayScore'])->name('exam.student.essay.score');
Route::get('/get-soal/{materialId}', [ScheduleExamController::class, 'getSoalByMaterial'])->name('exam.get.soal');

// Export routes
Route::get('/exam/{examId}/export/excel', [ExportController::class, 'exportExcel'])->name('exam.export.excel');
Route::get('/exam/{examId}/export/pdf', [ExportController::class, 'exportPdf'])->name('exam.export.pdf');
Route::get('/exam/{examId}/sync', [ExportController::class, 'syncData'])->name('exam.sync.data');

// Export routes with token authentication (for Flutter app)
Route::get('/export/{examId}/excel', [ExportController::class, 'exportExcelWithToken'])->name('export.excel.token');
Route::get('/export/{examId}/pdf', [ExportController::class, 'exportPdfWithToken'])->name('export.pdf.token');
