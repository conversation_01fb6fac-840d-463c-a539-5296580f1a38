class QuestionMaterial {
  final int id;
  final String name;
  final String description;
  final int pgCount;
  final int uraianCount;
  final int esaiCount;
  final int totalQuestions;
  final int? pgTotalScore;
  final int? uraianTotalScore;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  QuestionMaterial({
    required this.id,
    required this.name,
    required this.description,
    this.pgCount = 0,
    this.uraianCount = 0,
    this.esaiCount = 0,
    this.totalQuestions = 0,
    this.pgTotalScore,
    this.uraianTotalScore,
    this.createdAt,
    this.updatedAt,
  });

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  // Helper method to safely parse dynamic values to nullable int
  static int? _parseToNullableInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  factory QuestionMaterial.fromJson(Map<String, dynamic> json) {
    return QuestionMaterial(
      id: _parseToInt(json['id']),
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      pgCount: _parseToInt(json['pg_count']),
      uraianCount: _parseToInt(json['uraian_count']),
      esaiCount: _parseToInt(json['esai_count']),
      totalQuestions: _parseToInt(json['total_questions']),
      pgTotalScore: _parseToNullableInt(json['pg_total_score']),
      uraianTotalScore: _parseToNullableInt(json['uraian_total_score']),
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'pg_count': pgCount,
      'uraian_count': uraianCount,
      'esai_count': esaiCount,
      'total_questions': totalQuestions,
      'pg_total_score': pgTotalScore,
      'uraian_total_score': uraianTotalScore,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'QuestionMaterial{id: $id, name: $name, description: $description, pgCount: $pgCount, uraianCount: $uraianCount, esaiCount: $esaiCount, totalQuestions: $totalQuestions, pgTotalScore: $pgTotalScore, uraianTotalScore: $uraianTotalScore}';
  }
}
