import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'home_page.dart';
import 'admin_home_page.dart';
import 'student_home_page.dart';
import 'providers/auth_provider.dart';
import 'providers/question_material_provider.dart';
import 'providers/question_provider.dart';
import 'providers/teacher_provider.dart';
import 'providers/class_provider.dart';
import 'providers/student_provider.dart';
import 'providers/admin_provider.dart';
import 'providers/exam_provider.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set system chrome (status bar) color
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Color(0xFF455A9D),
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => QuestionMaterialProvider()),
        ChangeNotifierProvider(create: (context) => QuestionProvider()),
        ChangeNotifierProvider(create: (context) => TeacherProvider()),
        ChangeNotifierProvider(create: (context) => ClassProvider()),
        ChangeNotifierProvider(create: (context) => StudentProvider()),
        ChangeNotifierProvider(create: (context) => AdminProvider()),
        ChangeNotifierProvider(create: (context) => ExamProvider()),
      ],
      child: MaterialApp(
        title: 'ExamHots App',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const AuthWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Check authentication status when app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthStatus();
    });
  }

  Future<void> _checkAuthStatus() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.checkAuthStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Show loading while checking authentication
        if (authProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(color: Color(0xFF455A9D)),
            ),
          );
        }

        // Show login page if not authenticated
        if (!authProvider.isAuthenticated) {
          return const LoginPage();
        }

        // Navigate based on user role
        if (authProvider.user?.isAdmin == true) {
          return const AdminHomePage();
        } else if (authProvider.user?.isStudent == true) {
          return const StudentHomePage();
        } else {
          return const HomePage();
        }
      },
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _rememberMe = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    // Remove tab controller since we're removing registration tab
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    // Validate input
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.login(
      _emailController.text.trim(),
      _passwordController.text,
    );

    if (!mounted) return;

    if (success) {
      // Navigate based on user role
      Widget targetPage;
      if (authProvider.user?.isAdmin == true) {
        targetPage = const AdminHomePage();
      } else if (authProvider.user?.isStudent == true) {
        targetPage = const StudentHomePage();
      } else {
        targetPage = const HomePage();
      }

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => targetPage),
      );
    } else {
      // Show error message with snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            authProvider.errorMessage ??
                'Login gagal. Periksa email dan password Anda.',
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  void _showPrivacyPolicyModal() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: Color(0xFF455A9D),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Kebijakan Privasi',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPolicySection(
                          '1. Informasi yang Kami Kumpulkan',
                          'Kami mengumpulkan informasi yang Anda berikan secara langsung kepada kami, seperti nama lengkap, alamat email, dan informasi profil.',
                        ),
                        _buildPolicySection(
                          '2. Penggunaan Informasi',
                          'Informasi yang dikumpulkan digunakan untuk menyediakan layanan aplikasi, meningkatkan pengalaman pengguna, dan komunikasi terkait layanan.',
                        ),
                        _buildPolicySection(
                          '3. Keamanan Data',
                          'Kami berkomitmen untuk melindungi informasi pribadi Anda dengan menerapkan langkah-langkah keamanan yang sesuai.',
                        ),
                        _buildPolicySection(
                          '4. Kontak',
                          'Jika Anda memiliki pertanyaan tentang kebijakan privasi ini, silakan hubungi administrator sistem.',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showTermsModal() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: Color(0xFF455A9D),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Syarat dan Ketentuan',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPolicySection(
                          '1. Penerimaan Syarat',
                          'Dengan menggunakan aplikasi ExamHots, Anda menyetujui untuk terikat oleh syarat dan ketentuan ini.',
                        ),
                        _buildPolicySection(
                          '2. Penggunaan Aplikasi',
                          'Aplikasi ini disediakan untuk tujuan pendidikan. Pengguna diharapkan menggunakan aplikasi sesuai dengan tujuan yang dimaksud.',
                        ),
                        _buildPolicySection(
                          '3. Akun Pengguna',
                          'Pengguna bertanggung jawab untuk menjaga kerahasiaan akun dan password mereka.',
                        ),
                        _buildPolicySection(
                          '4. Pembatasan Tanggung Jawab',
                          'Aplikasi disediakan "sebagaimana adanya" tanpa jaminan apapun.',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showForgotPasswordModal() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.help_outline,
                  size: 64,
                  color: Color(0xFF455A9D),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Lupa Kata Sandi?',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Untuk mereset kata sandi Anda, silakan hubungi administrator sistem untuk mendapatkan bantuan.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF666666),
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          'Tutup',
                          style: TextStyle(
                            color: Color(0xFF666666),
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF455A9D),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Mengerti',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPolicySection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(color: Color(0xFFF8F9FD)),
        child: SafeArea(
          child: Stack(
            children: [
              // Background image at the bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Image.asset(
                  'assets/login-bg.jpg',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height:
                      MediaQuery.of(context).size.height *
                      0.3, // 30% of screen height
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback if image not found
                    return Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * 0.3,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xFFF8F9FD).withValues(alpha: 0.0),
                            const Color(0xFF455A9D).withValues(alpha: 0.2),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Main content
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight:
                          MediaQuery.of(context).size.height -
                          MediaQuery.of(context).padding.top,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Top content
                          Column(
                            children: [
                              const SizedBox(height: 40),
                              // Logo
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 10,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipOval(
                                  child: Image.asset(
                                    'assets/logo.png',
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      // Fallback if image not found
                                      return const Icon(
                                        Icons.school,
                                        size: 40,
                                        color: Color(0xFF455A9D),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(height: 32),
                              // Welcome Text
                              const Text(
                                'Selamat Datang! 👋',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF455A9D),
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Hai, Guru! Yuk, bikin soal & atur ujian jadi\nlebih mudah di sini!',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF666666),
                                  height: 1.4,
                                ),
                              ),
                              const SizedBox(height: 40),
                              // Combined Tab Bar and Login Form
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 10,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  children: [
                                    // Login Header
                                    Container(
                                      padding: const EdgeInsets.all(20),
                                      child: const Text(
                                        'Login',
                                        style: TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF455A9D),
                                        ),
                                      ),
                                    ),
                                    // Login Form
                                    Padding(
                                      padding: const EdgeInsets.all(24),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Email Field
                                          const Text(
                                            'Email*',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF333333),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFF5F5F5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: TextField(
                                              controller: _emailController,
                                              onChanged: (value) {
                                                // Clear error when user starts typing
                                                final authProvider =
                                                    Provider.of<AuthProvider>(
                                                      context,
                                                      listen: false,
                                                    );
                                                if (authProvider.errorMessage !=
                                                    null) {
                                                  authProvider.clearError();
                                                }
                                              },
                                              decoration: const InputDecoration(
                                                prefixIcon: Icon(
                                                  Icons.email_outlined,
                                                  color: Color(0xFF455A9D),
                                                ),
                                                border: InputBorder.none,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 16,
                                                    ),
                                                hintText:
                                                    '<EMAIL>',
                                                hintStyle: TextStyle(
                                                  color: Color(0xFF999999),
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 20),
                                          // Password Field
                                          const Text(
                                            'Password*',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF333333),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFF5F5F5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: TextField(
                                              controller: _passwordController,
                                              obscureText: _obscurePassword,
                                              onChanged: (value) {
                                                // Clear error when user starts typing
                                                final authProvider =
                                                    Provider.of<AuthProvider>(
                                                      context,
                                                      listen: false,
                                                    );
                                                if (authProvider.errorMessage !=
                                                    null) {
                                                  authProvider.clearError();
                                                }
                                              },
                                              decoration: InputDecoration(
                                                prefixIcon: const Icon(
                                                  Icons.lock_outline,
                                                  color: Color(0xFF455A9D),
                                                ),
                                                suffixIcon: IconButton(
                                                  icon: Icon(
                                                    _obscurePassword
                                                        ? Icons
                                                            .visibility_off_outlined
                                                        : Icons
                                                            .visibility_outlined,
                                                    color: const Color(
                                                      0xFF999999,
                                                    ),
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _obscurePassword =
                                                          !_obscurePassword;
                                                    });
                                                  },
                                                ),
                                                border: InputBorder.none,
                                                contentPadding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 16,
                                                    ),
                                                hintText:
                                                    'Type your password here',
                                                hintStyle: const TextStyle(
                                                  color: Color(0xFF999999),
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 20),
                                          // Remember Me and Forgot Password
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  Checkbox(
                                                    value: _rememberMe,
                                                    onChanged: (value) {
                                                      setState(() {
                                                        _rememberMe =
                                                            value ?? false;
                                                      });
                                                    },
                                                    activeColor: const Color(
                                                      0xFF455A9D,
                                                    ),
                                                  ),
                                                  const Text(
                                                    'Ingat Saya',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: Color(0xFF666666),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              TextButton(
                                                onPressed:
                                                    _showForgotPasswordModal,
                                                child: const Text(
                                                  'Lupa Kata Sandi ?',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: Color(0xFF455A9D),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 16),
                                          // Error Message Display
                                          Consumer<AuthProvider>(
                                            builder: (
                                              context,
                                              authProvider,
                                              child,
                                            ) {
                                              if (authProvider.errorMessage !=
                                                  null) {
                                                return Container(
                                                  width: double.infinity,
                                                  padding: const EdgeInsets.all(
                                                    12,
                                                  ),
                                                  margin: const EdgeInsets.only(
                                                    bottom: 16,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: Colors.red.shade50,
                                                    border: Border.all(
                                                      color:
                                                          Colors.red.shade200,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Icon(
                                                        Icons.error_outline,
                                                        color:
                                                            Colors.red.shade600,
                                                        size: 20,
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Expanded(
                                                        child: Text(
                                                          authProvider
                                                              .errorMessage!,
                                                          style: TextStyle(
                                                            color:
                                                                Colors
                                                                    .red
                                                                    .shade700,
                                                            fontSize: 14,
                                                          ),
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                        onTap: () {
                                                          authProvider
                                                              .clearError();
                                                        },
                                                        child: Icon(
                                                          Icons.close,
                                                          color:
                                                              Colors
                                                                  .red
                                                                  .shade600,
                                                          size: 18,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              }
                                              return const SizedBox.shrink();
                                            },
                                          ),
                                          // Login Button
                                          SizedBox(
                                            width: double.infinity,
                                            height: 50,
                                            child: Consumer<AuthProvider>(
                                              builder: (
                                                context,
                                                authProvider,
                                                child,
                                              ) {
                                                return ElevatedButton(
                                                  onPressed:
                                                      authProvider.isLoading
                                                          ? null
                                                          : () async {
                                                            await _handleLogin();
                                                          },
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        const Color(0xFF455A9D),
                                                    foregroundColor:
                                                        Colors.white,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            25,
                                                          ),
                                                    ),
                                                    elevation: 2,
                                                  ),
                                                  child:
                                                      authProvider.isLoading
                                                          ? const SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: CircularProgressIndicator(
                                                              strokeWidth: 2,
                                                              valueColor:
                                                                  AlwaysStoppedAnimation<
                                                                    Color
                                                                  >(
                                                                    Colors
                                                                        .white,
                                                                  ),
                                                            ),
                                                          )
                                                          : const Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Text(
                                                                'Login',
                                                                style: TextStyle(
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                width: 8,
                                                              ),
                                                              Icon(
                                                                Icons
                                                                    .arrow_forward,
                                                                size: 20,
                                                              ),
                                                            ],
                                                          ),
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 40),
                              // Footer
                              Wrap(
                                alignment: WrapAlignment.center,
                                children: [
                                  const Text(
                                    'Dengan Login Anda menyetujui ',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFF666666),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: _showPrivacyPolicyModal,
                                    child: const Text(
                                      'Kebijakan Privasi',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF455A9D),
                                        fontWeight: FontWeight.w500,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                  const Text(
                                    ' dan ',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFF666666),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: _showTermsModal,
                                    child: const Text(
                                      'Syarat dan Ketentuan Layanan',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF455A9D),
                                        fontWeight: FontWeight.w500,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 40),
                            ],
                          ),
                          // Bottom spacer to push content up from background image
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
