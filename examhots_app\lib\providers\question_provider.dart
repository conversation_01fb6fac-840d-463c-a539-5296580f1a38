import 'package:flutter/foundation.dart';
import '../models/question.dart';
import '../services/api_service.dart';
import 'package:image_picker/image_picker.dart';

class QuestionProvider with ChangeNotifier {
  List<Question> _questions = [];
  bool _isLoading = false;
  String? _errorMessage;
  int? _currentMaterialId;

  // Scores for each question type
  double _pgTotalScore = 0.0;
  double _uraianTotalScore = 0.0;
  double _esaiTotalScore = 0.0;

  List<Question> get questions => _questions;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int? get currentMaterialId => _currentMaterialId;

  // Getters for scores
  double get pgTotalScore => _pgTotalScore;
  double get uraianTotalScore => _uraianTotalScore;
  double get esaiTotalScore => _esaiTotalScore;

  // Getters for filtered questions
  List<Question> get pilihanGandaQuestions =>
      _questions.where((q) => q.type == 'pilihan_ganda').toList();

  List<Question> get uraianSingkatQuestions =>
      _questions.where((q) => q.type == 'uraian_singkat').toList();

  List<Question> get esaiQuestions =>
      _questions.where((q) => q.type == 'esai').toList();

  // Getters for question counts
  int get pgCount => pilihanGandaQuestions.length;
  int get uraianCount => uraianSingkatQuestions.length;
  int get esaiCount => esaiQuestions.length;
  int get totalQuestions => _questions.length;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error message
  void _setError(String error) {
    _errorMessage = error;
    _setLoading(false);
  }

  // Update scores
  void updatePgScore(double score) {
    _pgTotalScore = score;
    notifyListeners();
  }

  void updateUraianScore(double score) {
    _uraianTotalScore = score;
    notifyListeners();
  }

  void updateEsaiScore(double score) {
    _esaiTotalScore = score;
    notifyListeners();
  }

  // Save scores to database
  Future<bool> saveScoresToDatabase() async {
    if (_currentMaterialId == null) {
      _setError('Material ID not found');
      return false;
    }

    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateQuestionMaterialScores(
        _currentMaterialId!,
        _pgTotalScore,
        _uraianTotalScore,
      );

      if (response['success'] == true) {
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to save scores');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load questions by material ID
  Future<void> loadQuestionsByMaterialId(int materialId) async {
    try {
      _setLoading(true);
      clearError();
      _currentMaterialId = materialId;

      final response = await ApiService.getQuestionsByMaterialId(materialId);

      if (response['success'] == true) {
        final data = response['data'];

        // Extract questions from the response
        if (data['questions'] != null) {
          final List<dynamic> questionsData = data['questions'];
          _questions =
              questionsData.map((json) => Question.fromJson(json)).toList();
        } else {
          _questions = [];
        }

        // Load saved scores from database or initialize default scores
        _loadScoresFromData(data);
      } else {
        _setError(response['message'] ?? 'Failed to load questions');
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
    } finally {
      _setLoading(false);
    }
  }

  // Helper method to safely parse dynamic values to double
  double _parseToDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Load scores from API data or initialize default scores
  void _loadScoresFromData(Map<String, dynamic> data) {
    // Load saved scores from database if available with safe parsing
    _pgTotalScore =
        _parseToDouble(data['pg_total_score']) > 0
            ? _parseToDouble(data['pg_total_score'])
            : (pgCount * 10.0); // Default 10 points per PG question

    _uraianTotalScore =
        _parseToDouble(data['uraian_total_score']) > 0
            ? _parseToDouble(data['uraian_total_score'])
            : (uraianCount * 15.0); // Default 15 points per uraian question

    // Calculate esai total score from individual question scores
    _esaiTotalScore = _calculateEsaiTotalScore();
  }

  // Calculate total score for esai questions from individual scores
  double _calculateEsaiTotalScore() {
    double total = 0.0;
    for (final question in esaiQuestions) {
      if (question.answers.isNotEmpty) {
        final answer = question.answers.first;
        if (answer.score != null) {
          total += answer.score!.toDouble();
        }
      }
    }
    return total;
  }

  // Initialize default scores
  void _initializeScores() {
    // Set default scores (can be customized later)
    _pgTotalScore = pgCount * 10.0; // Default 10 points per PG question
    _uraianTotalScore =
        uraianCount * 15.0; // Default 15 points per uraian question

    // Calculate esai total score from individual question scores
    _esaiTotalScore = _calculateEsaiTotalScore();
  }

  // Clear all data
  void clear() {
    _questions = [];
    _currentMaterialId = null;
    _pgTotalScore = 0.0;
    _uraianTotalScore = 0.0;
    _esaiTotalScore = 0.0;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  // Refresh questions
  Future<void> refresh() async {
    if (_currentMaterialId != null) {
      await loadQuestionsByMaterialId(_currentMaterialId!);
    }
  }

  // Recalculate esai total score (call this when esai questions are added/updated/deleted)
  void recalculateEsaiScore() {
    _esaiTotalScore = _calculateEsaiTotalScore();
    notifyListeners();
  }

  // Update question
  Future<bool> updateQuestion({
    required int questionId,
    required String question,
    required String type,
    List<String>? imagePaths,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateQuestion(
        questionId: questionId,
        question: question,
        type: type,
        imagePaths: imagePaths,
        answers: answers,
        correctAnswerIndex: correctAnswerIndex,
        answer: answer,
      );

      if (response['success'] == true) {
        // Update the question in the list
        final updatedQuestion = Question.fromJson(response['data']);
        final index = _questions.indexWhere((q) => q.id == questionId);
        if (index != -1) {
          _questions[index] = updatedQuestion;
          // Recalculate esai score if this was an esai question
          if (type == 'esai') {
            recalculateEsaiScore();
          } else {
            notifyListeners();
          }
        }
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to update question');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update question with XFile support (web-compatible)
  Future<bool> updateQuestionWithFiles({
    required int questionId,
    required String question,
    required String type,
    List<XFile>? imageFiles,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
    double? score,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateQuestionWithFiles(
        questionId: questionId,
        question: question,
        type: type,
        imageFiles: imageFiles,
        answers: answers,
        correctAnswerIndex: correctAnswerIndex,
        answer: answer,
        score: score,
      );

      if (response['success'] == true) {
        // Update the question in the list
        final updatedQuestion = Question.fromJson(response['data']);
        final index = _questions.indexWhere((q) => q.id == questionId);
        if (index != -1) {
          _questions[index] = updatedQuestion;
          // Recalculate esai score if this was an esai question
          if (type == 'esai') {
            recalculateEsaiScore();
          } else {
            notifyListeners();
          }
        }
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to update question');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete question
  Future<bool> deleteQuestion(int questionId) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.deleteQuestion(questionId);

      if (response['success'] == true) {
        // Check if the deleted question was an esai question
        final deletedQuestion = _questions.firstWhere(
          (q) => q.id == questionId,
        );
        final wasEsaiQuestion = deletedQuestion.type == 'esai';

        // Remove the question from the list
        _questions.removeWhere((q) => q.id == questionId);

        // Recalculate scores
        if (wasEsaiQuestion) {
          // Recalculate esai score and other scores
          _initializeScores();
        } else {
          // Just recalculate default scores for PG and uraian
          _pgTotalScore = pgCount * 10.0;
          _uraianTotalScore = uraianCount * 15.0;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to delete question');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get total score for all question types
  double get totalScore => _pgTotalScore + _uraianTotalScore + _esaiTotalScore;

  // Check if there are any questions
  bool get hasQuestions => _questions.isNotEmpty;
  bool get hasNoQuestions => _questions.isEmpty;
}
