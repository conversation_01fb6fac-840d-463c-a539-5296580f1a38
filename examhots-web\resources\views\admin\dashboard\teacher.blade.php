@extends('main')

@section('content')
    <div class="max-w-7xl mx-auto">
        <!-- Welcome Section -->
        <div class="mb-8">
            <div class="bg-gradient-to-r from-primary-blue to-secondary-blue rounded-2xl p-8 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">Selamat Datang, {{ Auth::user()->name }}!</h1>
                        <p class="text-blue-100 text-lg">Dashboard Guru - Kelola bank soal dan ujian dengan mudah</p>
                    </div>
                    <div class="hidden md:block">
                        <iconify-icon icon="mdi:account-tie" width="80" height="80"
                            class="text-blue-200"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Total Question Banks -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Bank Soal</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_question_banks']) }}</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:book-open-variant" width="24" height="24"
                            class="text-indigo-600"></iconify-icon>
                    </div>
                </div>
            </div>

            <!-- Total Questions -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Total Soal</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_questions']) }}</p>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:help-circle" width="24" height="24"
                            class="text-yellow-600"></iconify-icon>
                    </div>
                </div>
            </div>

            <!-- Total Exams -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Total Ujian</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_exams']) }}</p>
                    </div>
                    <div class="bg-red-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:clipboard-text" width="24" height="24"
                            class="text-red-600"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exam Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Active Exams -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Ujian Aktif</p>
                        <p class="text-2xl font-bold text-green-600">{{ number_format($stats['active_exams']) }}</p>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:clock-check" width="24" height="24"
                            class="text-green-600"></iconify-icon>
                    </div>
                </div>
            </div>

            <!-- Upcoming Exams -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Ujian Mendatang</p>
                        <p class="text-2xl font-bold text-blue-600">{{ number_format($stats['upcoming_exams']) }}</p>
                    </div>
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:clock-outline" width="24" height="24"
                            class="text-blue-600"></iconify-icon>
                    </div>
                </div>
            </div>

            <!-- Completed Exams -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm font-medium">Ujian Selesai</p>
                        <p class="text-2xl font-bold text-gray-600">{{ number_format($stats['completed_exams']) }}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <iconify-icon icon="mdi:check-circle" width="24" height="24"
                            class="text-gray-600"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- Bank Soal -->
            <a href="{{ route('question.index') }}"
                class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all hover:border-indigo-200 group">
                <div class="flex items-center space-x-4">
                    <div class="bg-indigo-50 p-4 rounded-lg group-hover:bg-indigo-100 transition-colors">
                        <iconify-icon icon="mdi:book-open-variant" width="32" height="32"
                            class="text-indigo-600"></iconify-icon>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Kelola Bank Soal</h3>
                        <p class="text-sm text-gray-500">Buat dan kelola bank soal untuk ujian</p>
                    </div>
                    <div class="ml-auto">
                        <iconify-icon icon="mdi:arrow-right" width="20" height="20"
                            class="text-gray-400 group-hover:text-indigo-600 transition-colors"></iconify-icon>
                    </div>
                </div>
            </a>

            <!-- Jadwal Ujian -->
            <a href="{{ route('exam.index') }}"
                class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all hover:border-red-200 group">
                <div class="flex items-center space-x-4">
                    <div class="bg-red-50 p-4 rounded-lg group-hover:bg-red-100 transition-colors">
                        <iconify-icon icon="mdi:calendar-clock" width="32" height="32"
                            class="text-red-600"></iconify-icon>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Jadwal Ujian</h3>
                        <p class="text-sm text-gray-500">Atur jadwal dan pelaksanaan ujian</p>
                    </div>
                    <div class="ml-auto">
                        <iconify-icon icon="mdi:arrow-right" width="20" height="20"
                            class="text-gray-400 group-hover:text-red-600 transition-colors"></iconify-icon>
                    </div>
                </div>
            </a>
        </div>

        <!-- Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Upcoming Exams -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Ujian Mendatang</h2>
                    <a href="{{ route('exam.index') }}" class="text-primary-blue hover:text-blue-700 text-sm font-medium">
                        Lihat Semua
                    </a>
                </div>

                @if ($upcoming_exams->count() > 0)
                    <div class="space-y-4">
                        @foreach ($upcoming_exams as $exam)
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h3 class="font-medium text-gray-900">{{ $exam->name }}</h3>
                                        <p class="text-sm text-gray-500 mt-1">{{ $exam->questionmaterial->name ?? 'N/A' }}
                                        </p>
                                        <p class="text-xs text-gray-400 mt-1">Kelas: {{ $exam->class->name ?? 'N/A' }}</p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="inline-flex items-center text-xs text-blue-600">
                                                <iconify-icon icon="mdi:calendar" width="14" height="14"
                                                    class="mr-1"></iconify-icon>
                                                {{ \Carbon\Carbon::parse($exam->startdate)->format('d M Y') }}
                                            </span>
                                            <span class="inline-flex items-center text-xs text-green-600">
                                                <iconify-icon icon="mdi:clock" width="14" height="14"
                                                    class="mr-1"></iconify-icon>
                                                {{ $exam->starttime }} - {{ $exam->endtime }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $exam->duration }} menit
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="mdi:calendar-blank" width="48" height="48"
                            class="text-gray-300 mx-auto mb-3"></iconify-icon>
                        <p class="text-gray-500">Tidak ada ujian mendatang</p>
                        <button onclick="openCreateExamModal()"
                            class="text-primary-blue hover:text-blue-700 text-sm font-medium mt-2 inline-block">
                            Buat Ujian Baru
                        </button>
                    </div>
                @endif
            </div>

            <!-- Question Bank Statistics -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Statistik Bank Soal</h2>
                    <a href="{{ route('question.index') }}"
                        class="text-primary-blue hover:text-blue-700 text-sm font-medium">
                        Lihat Semua
                    </a>
                </div>

                @if ($question_stats->count() > 0)
                    <div class="space-y-4">
                        @foreach ($question_stats->take(5) as $material)
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">{{ $material->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ Str::limit($material->description, 50) }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-primary-blue">{{ $material->questions_count }}
                                    </p>
                                    <p class="text-xs text-gray-500">soal</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <iconify-icon icon="mdi:book-open-variant" width="48" height="48"
                            class="text-gray-300 mx-auto mb-3"></iconify-icon>
                        <p class="text-gray-500">Belum ada bank soal</p>
                        <a href="{{ route('question.add') }}"
                            class="text-primary-blue hover:text-blue-700 text-sm font-medium mt-2 inline-block">
                            Buat Bank Soal Baru
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Exams -->
        @if ($recent_exams->count() > 0)
            <div class="mt-8">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Ujian Terbaru</h2>
                        <a href="{{ route('exam.index') }}"
                            class="text-primary-blue hover:text-blue-700 text-sm font-medium">
                            Lihat Semua
                        </a>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach ($recent_exams->take(3) as $exam)
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                                <h3 class="font-medium text-gray-900 mb-2">{{ $exam->name }}</h3>
                                <p class="text-sm text-gray-500 mb-3">{{ $exam->questionmaterial->name ?? 'N/A' }}</p>

                                <div class="space-y-2">
                                    <div class="flex items-center justify-between text-xs">
                                        <span class="text-gray-500">Kelas:</span>
                                        <span class="font-medium">{{ $exam->class->name ?? 'N/A' }}</span>
                                    </div>
                                    <div class="flex items-center justify-between text-xs">
                                        <span class="text-gray-500">Tanggal:</span>
                                        <span
                                            class="font-medium">{{ \Carbon\Carbon::parse($exam->startdate)->format('d M Y') }}</span>
                                    </div>
                                    <div class="flex items-center justify-between text-xs">
                                        <span class="text-gray-500">Waktu:</span>
                                        <span class="font-medium">{{ $exam->starttime }} - {{ $exam->endtime }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Modal Global -->
    <div id="modalGlobal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div id="modalContent"
            class="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
@endsection

<script>
    // Ensure functions are available globally
    window.openCreateExamModal = function() {
        fetch("{{ route('exam.add') }}")
            .then(response => response.text())
            .then(html => {
                const modal = document.getElementById('modalGlobal');
                const content = document.getElementById('modalContent');

                content.innerHTML = html;
                modal.classList.remove('hidden');

                // Add animation
                setTimeout(() => {
                    content.classList.remove('scale-95', 'opacity-0');
                    content.classList.add('scale-100', 'opacity-100');
                }, 10);
            })
            .catch(error => {
                console.error('Error loading modal:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Gagal memuat form ujian. Silakan coba lagi.',
                    confirmButtonColor: '#455A9D'
                });
            });
    };

    window.closeModal = function() {
        const modal = document.getElementById('modalGlobal');
        const content = document.getElementById('modalContent');

        content.classList.remove('scale-100', 'opacity-100');
        content.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            content.innerHTML = '';
        }, 300);
    };

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('modalGlobal');
        if (e.target === modal) {
            closeModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('modalGlobal');
            if (!modal.classList.contains('hidden')) {
                closeModal();
            }
        }
    });
</script>
