{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\android\\app\\.cxx\\RelWithDebInfo\\4e4t2q5y\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp-8.2-new\\htdocs\\examhots\\examhots_app\\android\\app\\.cxx\\RelWithDebInfo\\4e4t2q5y\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}