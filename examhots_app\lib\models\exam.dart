class Exam {
  final int id;
  final String name;
  final String startdate;
  final String enddate;
  final String starttime;
  final String endtime;
  final int duration;
  final String token;
  final String kkm;
  final int amountquestion;
  final int trials;
  final QuestionMaterialInfo? questionmaterial;
  final ClassInfo? classInfo;
  final List<QuestionInfo>? questions;
  final String? createdAt;
  final String? updatedAt;

  Exam({
    required this.id,
    required this.name,
    required this.startdate,
    required this.enddate,
    required this.starttime,
    required this.endtime,
    required this.duration,
    required this.token,
    required this.kkm,
    required this.amountquestion,
    required this.trials,
    this.questionmaterial,
    this.classInfo,
    this.questions,
    this.createdAt,
    this.updatedAt,
  });

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory Exam.fromJson(Map<String, dynamic> json) {
    return Exam(
      id: _parseToInt(json['id']),
      name: json['name'] ?? '',
      startdate: json['startdate'] ?? '',
      enddate: json['enddate'] ?? '',
      starttime: json['starttime'] ?? '',
      endtime: json['endtime'] ?? '',
      duration: _parseToInt(json['duration']),
      token: json['token'] ?? '',
      kkm: json['kkm']?.toString() ?? '',
      amountquestion: _parseToInt(json['amountquestion']),
      trials: _parseToInt(json['trials']),
      questionmaterial:
          json['questionmaterial'] != null
              ? QuestionMaterialInfo.fromJson(json['questionmaterial'])
              : null,
      classInfo:
          json['class'] != null ? ClassInfo.fromJson(json['class']) : null,
      questions:
          json['questions'] != null
              ? (json['questions'] as List)
                  .map((q) => QuestionInfo.fromJson(q))
                  .toList()
              : null,
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'startdate': startdate,
      'enddate': enddate,
      'starttime': starttime,
      'endtime': endtime,
      'duration': duration,
      'token': token,
      'kkm': kkm,
      'amountquestion': amountquestion,
      'trials': trials,
      'questionmaterial': questionmaterial?.toJson(),
      'class': classInfo?.toJson(),
      'questions': questions?.map((q) => q.toJson()).toList(),
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // Helper method to get formatted date range
  String get dateRange {
    if (startdate == enddate) {
      return startdate;
    }
    return '$startdate - $enddate';
  }

  // Helper method to get formatted time range
  String get timeRange {
    return '$starttime - $endtime';
  }

  // Helper method to get duration in hours and minutes
  String get formattedDuration {
    final hours = duration ~/ 60;
    final minutes = duration % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}m';
    }
  }

  // Helper method to check if exam is active
  bool get isActive {
    try {
      final now = DateTime.now();
      final startDateTime = _parseDateTime(startdate, starttime);
      final endDateTime = _parseDateTime(enddate, endtime);

      return now.isAfter(startDateTime) && now.isBefore(endDateTime);
    } catch (e) {
      print('Error parsing exam dates: $e');
      return false;
    }
  }

  // Helper method to parse date and time safely
  DateTime _parseDateTime(String date, String time) {
    // Clean up time format - remove extra :00 if present
    String cleanTime = time;
    if (time.split(':').length > 3) {
      // If time has more than 3 parts (HH:MM:SS:XX), take only first 3
      final parts = time.split(':');
      cleanTime = '${parts[0]}:${parts[1]}:${parts[2]}';
    }

    return DateTime.parse('$date $cleanTime');
  }

  // Helper method to check if exam is upcoming
  bool get isUpcoming {
    try {
      final now = DateTime.now();
      final startDateTime = _parseDateTime(startdate, starttime);

      return now.isBefore(startDateTime);
    } catch (e) {
      return false;
    }
  }

  // Helper method to check if exam is finished
  bool get isFinished {
    try {
      final now = DateTime.now();
      final endDateTime = _parseDateTime(enddate, endtime);

      return now.isAfter(endDateTime);
    } catch (e) {
      return false;
    }
  }

  // Helper method to get exam status
  String get status {
    if (isActive) {
      return 'Berlangsung';
    } else if (isUpcoming) {
      return 'Akan Datang';
    } else {
      return 'Selesai';
    }
  }

  // Helper method to get status color
  String get statusColor {
    if (isActive) {
      return '#10B981'; // Green
    } else if (isUpcoming) {
      return '#F59E0B'; // Orange
    } else {
      return '#6B7280'; // Gray
    }
  }
}

class QuestionMaterialInfo {
  final int id;
  final String name;

  QuestionMaterialInfo({required this.id, required this.name});

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory QuestionMaterialInfo.fromJson(Map<String, dynamic> json) {
    return QuestionMaterialInfo(
      id: _parseToInt(json['id']),
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name};
  }
}

class ClassInfo {
  final int id;
  final String name;

  ClassInfo({required this.id, required this.name});

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory ClassInfo.fromJson(Map<String, dynamic> json) {
    return ClassInfo(id: _parseToInt(json['id']), name: json['name'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name};
  }
}

class QuestionInfo {
  final int id;
  final String question;
  final String type;

  QuestionInfo({required this.id, required this.question, required this.type});

  // Helper method to safely parse dynamic values to int
  static int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  factory QuestionInfo.fromJson(Map<String, dynamic> json) {
    return QuestionInfo(
      id: _parseToInt(json['id']),
      question: json['question'] ?? '',
      type: json['type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'question': question, 'type': type};
  }
}

// Model for creating/updating exam
class ExamRequest {
  final String name;
  final String startdate;
  final String enddate;
  final String starttime;
  final String endtime;
  final int duration;
  final String kkm;
  final int classid;
  final int questionmaterialid;
  final int trials;
  final List<int> questionIds;

  ExamRequest({
    required this.name,
    required this.startdate,
    required this.enddate,
    required this.starttime,
    required this.endtime,
    required this.duration,
    required this.kkm,
    required this.classid,
    required this.questionmaterialid,
    required this.trials,
    required this.questionIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'startdate': startdate,
      'enddate': enddate,
      'starttime': starttime,
      'endtime': endtime,
      'duration': duration,
      'kkm': kkm,
      'classid': classid,
      'questionmaterialid': questionmaterialid,
      'trials': trials,
      'question_ids': questionIds,
    };
  }
}

// Model for form dropdown data
class ExamFormData {
  final List<ClassInfo> classes;
  final List<QuestionMaterialInfo> questionMaterials;

  ExamFormData({required this.classes, required this.questionMaterials});

  factory ExamFormData.fromJson(Map<String, dynamic> json) {
    return ExamFormData(
      classes:
          (json['classes'] as List).map((c) => ClassInfo.fromJson(c)).toList(),
      questionMaterials:
          (json['questionMaterials'] as List)
              .map((qm) => QuestionMaterialInfo.fromJson(qm))
              .toList(),
    );
  }
}
